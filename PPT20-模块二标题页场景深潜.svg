<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#003366;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#001122;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="diveGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00ccff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0066cc;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#87ceeb;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4682b4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#87ceeb;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- 背景渐变 -->
  <rect width="1920" height="1080" fill="url(#headerGradient)"/>
  
  <!-- 波浪装饰 -->
  <g opacity="0.3">
    <path d="M0,300 Q480,250 960,300 T1920,300 L1920,400 Q1440,350 960,400 T0,400 Z" fill="url(#waveGradient)"/>
    <path d="M0,500 Q480,450 960,500 T1920,500 L1920,600 Q1440,550 960,600 T0,600 Z" fill="url(#waveGradient)"/>
    <path d="M0,700 Q480,650 960,700 T1920,700 L1920,800 Q1440,750 960,800 T0,800 Z" fill="url(#waveGradient)"/>
  </g>
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="white">
    模块二
  </text>
  
  <text x="960" y="300" text-anchor="middle" font-family="Microsoft YaHei" font-size="84" font-weight="bold" fill="white">
    场景深潜
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="380" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#87ceeb">
    深入理解客户需求，精准匹配解决方案
  </text>
  
  <!-- 潜水员图标 -->
  <g transform="translate(960, 450)">
    <circle cx="0" cy="0" r="80" fill="url(#diveGradient)" opacity="0.8"/>
    <circle cx="0" cy="-20" r="25" fill="white"/>
    <circle cx="-8" cy="-25" r="5" fill="#333333"/>
    <circle cx="8" cy="-25" r="5" fill="#333333"/>
    <path d="M-15,-10 Q0,-5 15,-10" stroke="white" stroke-width="3" fill="none"/>
    
    <!-- 氧气管 -->
    <path d="M25,-20 Q40,-15 50,-10 Q60,-5 70,0" stroke="#ff9900" stroke-width="4" fill="none"/>
    <circle cx="70" cy="0" r="8" fill="#ff9900"/>
    
    <!-- 气泡 -->
    <circle cx="-30" cy="-60" r="8" fill="white" opacity="0.7"/>
    <circle cx="-20" cy="-80" r="6" fill="white" opacity="0.6"/>
    <circle cx="-10" cy="-95" r="4" fill="white" opacity="0.5"/>
    
    <circle cx="30" cy="-70" r="7" fill="white" opacity="0.6"/>
    <circle cx="40" cy="-90" r="5" fill="white" opacity="0.5"/>
  </g>
  
  <!-- 学习目标 -->
  <g transform="translate(300, 600)">
    <rect x="0" y="0" width="1320" height="200" rx="20" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
    <text x="660" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">
      学习目标
    </text>
    
    <g transform="translate(100, 80)">
      <circle cx="0" cy="0" r="8" fill="#00ccff"/>
      <text x="30" y="5" font-family="Microsoft YaHei" font-size="24" fill="white">
        深度理解智慧派出所八大应用场景
      </text>
      
      <circle cx="0" cy="40" r="8" fill="#00ccff"/>
      <text x="30" y="45" font-family="Microsoft YaHei" font-size="24" fill="white">
        精准识别各场景核心痛点和需求
      </text>
      
      <circle cx="0" cy="80" r="8" fill="#00ccff"/>
      <text x="30" y="85" font-family="Microsoft YaHei" font-size="24" fill="white">
        掌握电信解决方案的匹配逻辑和价值主张
      </text>
    </g>
  </g>
  
  <!-- 场景预览 -->
  <g transform="translate(200, 850)">
    <text x="760" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
      八大核心场景
    </text>
    
    <g transform="translate(0, 40)">
      <rect x="0" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="90" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">AI视频监控</text>
      
      <rect x="200" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="290" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">移动警务</text>
      
      <rect x="400" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="490" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">物联感知</text>
      
      <rect x="600" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="690" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">数据治理</text>
      
      <rect x="800" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="890" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">指挥调度</text>
      
      <rect x="1000" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="1090" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">综合应用</text>
      
      <rect x="1200" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="1290" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">网络安全</text>
      
      <rect x="1400" y="0" width="180" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
      <text x="1490" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="white">运维保障</text>
    </g>
  </g>
  
  <!-- 底部装饰线 -->
  <rect x="0" y="1050" width="1920" height="30" fill="rgba(255,255,255,0.2)"/>
  <text x="960" y="1070" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">
    深潜场景海洋，发现客户需求珍珠
  </text>
</svg>
