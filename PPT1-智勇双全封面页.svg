<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,200 Q480,150 960,200 T1920,200" stroke="#0066cc" stroke-width="3" fill="none" opacity="0.3"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" stroke="#0066cc" stroke-width="3" fill="none" opacity="0.3"/>
  
  <!-- 科技装饰元素 -->
  <circle cx="1600" cy="300" r="80" fill="none" stroke="#0066cc" stroke-width="2" opacity="0.2"/>
  <circle cx="1600" cy="300" r="60" fill="none" stroke="#0066cc" stroke-width="1" opacity="0.3"/>
  <circle cx="320" cy="800" r="60" fill="none" stroke="#0066cc" stroke-width="2" opacity="0.2"/>
  
  <!-- 主标题 -->
  <text x="960" y="380" text-anchor="middle" font-family="Microsoft YaHei" font-size="88" font-weight="bold" fill="url(#accentGradient)">
    智勇双全·赢在警务
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="480" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#333333">
    中国电信智慧派出所实战营销与方案赋能
  </text>
  
  <!-- Day 1标识 -->
  <rect x="820" y="520" width="280" height="60" rx="30" fill="url(#accentGradient)"/>
  <text x="960" y="560" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
    Day 1 理论集训
  </text>
  
  <!-- 中国电信Logo区域 -->
  <rect x="100" y="100" width="200" height="80" rx="10" fill="#0066cc" opacity="0.1"/>
  <text x="200" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
    中国电信
  </text>
  
  <!-- 讲师信息 -->
  <text x="960" y="720" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#666666">
    主讲培训师：[您的姓名] | [您的职位]
  </text>
  
  <!-- 日期地点 -->
  <text x="960" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#666666">
    [培训日期] | [培训地点]
  </text>
  
  <!-- 科技线条装饰 -->
  <g opacity="0.2">
    <line x1="100" y1="950" x2="400" y2="950" stroke="#0066cc" stroke-width="2"/>
    <line x1="1520" y1="950" x2="1820" y2="950" stroke="#0066cc" stroke-width="2"/>
    <circle cx="420" cy="950" r="5" fill="#0066cc"/>
    <circle cx="1500" cy="950" r="5" fill="#0066cc"/>
  </g>
</svg>
