<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#cc3366;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#992244;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff0f8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffe6f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    感谢与祝愿
  </text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 200 Q 960 150 1820 200" stroke="#cc3366" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M 100 220 Q 960 170 1820 220" stroke="#ff6699" stroke-width="3" fill="none" opacity="0.4"/>
  
  <!-- 感谢内容 -->
  <g transform="translate(200, 280)">
    <rect x="0" y="0" width="1520" height="500" rx="30" fill="url(#cardGradient)" stroke="#cc3366" stroke-width="3"/>
    
    <!-- 感谢标题 -->
    <text x="760" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#cc3366">
      衷心感谢各位学员的积极参与
    </text>
    
    <!-- 感谢内容 -->
    <g transform="translate(100, 120)">
      <text x="660" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#333333">
        感谢大家在本次培训中的：
      </text>
      
      <g transform="translate(0, 60)">
        <circle cx="30" cy="15" r="8" fill="#cc3366"/>
        <text x="60" y="20" font-family="Microsoft YaHei" font-size="28" fill="#333333">
          <tspan font-weight="bold" fill="#cc3366">认真学习</tspan> - 专注听讲，积极思考，深度参与每一个环节
        </text>
        
        <circle cx="30" cy="65" r="8" fill="#cc3366"/>
        <text x="60" y="70" font-family="Microsoft YaHei" font-size="28" fill="#333333">
          <tspan font-weight="bold" fill="#cc3366">热烈讨论</tspan> - 踊跃发言，分享经验，碰撞思维火花
        </text>
        
        <circle cx="30" cy="115" r="8" fill="#cc3366"/>
        <text x="60" y="120" font-family="Microsoft YaHei" font-size="28" fill="#333333">
          <tspan font-weight="bold" fill="#cc3366">实战演练</tspan> - 团队协作，创新思维，展现专业素养
        </text>
        
        <circle cx="30" cy="165" r="8" fill="#cc3366"/>
        <text x="60" y="170" font-family="Microsoft YaHei" font-size="28" fill="#333333">
          <tspan font-weight="bold" fill="#cc3366">经验分享</tspan> - 开放交流，互相学习，共同提升
        </text>
        
        <circle cx="30" cy="215" r="8" fill="#cc3366"/>
        <text x="60" y="220" font-family="Microsoft YaHei" font-size="28" fill="#333333">
          <tspan font-weight="bold" fill="#cc3366">持续改进</tspan> - 反思总结，制定计划，追求卓越
        </text>
      </g>
      
      <!-- 特别感谢 -->
      <rect x="0" y="300" width="1320" height="120" rx="20" fill="#fff0f8" stroke="#ff6699" stroke-width="2"/>
      <text x="660" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#cc3366">
        特别感谢
      </text>
      <text x="660" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        感谢各位领导的大力支持，感谢各位同事的密切配合
      </text>
      <text x="660" y="105" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        感谢培训组织团队的精心安排，感谢技术支持团队的保障服务
      </text>
    </g>
  </g>
  
  <!-- 祝愿内容 -->
  <g transform="translate(200, 820)">
    <rect x="0" y="0" width="1520" height="180" rx="30" fill="#f0f8ff" stroke="#3366cc" stroke-width="3"/>
    
    <text x="760" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#3366cc">
      美好祝愿
    </text>
    
    <g transform="translate(100, 80)">
      <text x="660" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#333333">
        祝愿各位学员：
      </text>
      <text x="660" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#3366cc">
        学以致用，业绩突破，事业有成！
      </text>
      <text x="660" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#333333">
        在智慧派出所的广阔天地中展现才华，创造辉煌！
      </text>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 1020 Q 960 970 1820 1020" stroke="#3366cc" stroke-width="4" fill="none" opacity="0.6"/>
  <path d="M 100 1040 Q 960 990 1820 1040" stroke="#6699ff" stroke-width="3" fill="none" opacity="0.4"/>
</svg>
