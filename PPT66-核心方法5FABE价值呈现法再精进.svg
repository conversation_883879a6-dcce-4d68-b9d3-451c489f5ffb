<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    沟通利器：FABE话术再精进
  </text>
  
  <!-- FABE模型回顾 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="140" rx="20" fill="#e6f3ff" stroke="#0066cc" stroke-width="3"/>
    <text x="860" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">
      FABE模型回顾
    </text>
    
    <g transform="translate(50, 70)">
      <g transform="translate(0, 0)">
        <rect x="0" y="0" width="400" height="60" rx="10" fill="#ff6666" opacity="0.9"/>
        <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
          F - Feature 特性
        </text>
        <text x="200" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">
          产品/方案的功能特点
        </text>
      </g>
      
      <g transform="translate(420, 0)">
        <rect x="0" y="0" width="400" height="60" rx="10" fill="#00cc66" opacity="0.9"/>
        <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
          A - Advantage 优势
        </text>
        <text x="200" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">
          相比竞争对手的优势
        </text>
      </g>
      
      <g transform="translate(840, 0)">
        <rect x="0" y="0" width="400" height="60" rx="10" fill="#ff9900" opacity="0.9"/>
        <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
          B - Benefit 收益
        </text>
        <text x="200" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">
          客户获得的价值收益
        </text>
      </g>
      
      <g transform="translate(1260, 0)">
        <rect x="0" y="0" width="400" height="60" rx="10" fill="#9966cc" opacity="0.9"/>
        <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
          E - Evidence 证据
        </text>
        <text x="200" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">
          支撑收益的有力证据
        </text>
      </g>
    </g>
  </g>
  
  <!-- 强调关键 -->
  <g transform="translate(50, 360)">
    <rect x="0" y="0" width="1820" height="280" rx="20" fill="#fff8f0" stroke="#ff9900" stroke-width="3"/>
    <text x="910" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff9900">
      强调关键要点
    </text>
    
    <g transform="translate(80, 70)">
      <!-- B收益关键 -->
      <rect x="0" y="0" width="820" height="180" rx="15" fill="#fff0f0" stroke="#ff6666" stroke-width="2"/>
      <text x="410" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        B（收益）关键要求
      </text>
      
      <g transform="translate(30, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6666">
          必须与客户痛点/目标强关联：
        </text>
        <text x="20" y="30" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 针对性：直击客户核心痛点
        </text>
        <text x="20" y="55" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 量化性：用数据说话，具体可感
        </text>
        <text x="20" y="80" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 时效性：明确收益实现时间
        </text>
        <text x="20" y="105" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 差异性：突出独特价值主张
        </text>
      </g>
      
      <!-- E证据关键 -->
      <rect x="860" y="0" width="820" height="180" rx="15" fill="#f0f0ff" stroke="#9966cc" stroke-width="2"/>
      <text x="1270" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#9966cc">
        E（证据）关键要求
      </text>
      
      <g transform="translate(890, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#9966cc">
          必须真实可信（优先级排序）：
        </text>
        <text x="20" y="30" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 案例证据：成功案例最有说服力
        </text>
        <text x="20" y="55" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 数据证据：权威数据支撑
        </text>
        <text x="20" y="80" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 承诺证据：服务保障承诺
        </text>
        <text x="20" y="105" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 第三方认证：权威机构认可
        </text>
      </g>
    </g>
  </g>
  
  <!-- 使用技巧 -->
  <g transform="translate(50, 680)">
    <rect x="0" y="0" width="1820" height="200" rx="20" fill="#f0fff0" stroke="#00cc66" stroke-width="3"/>
    <text x="910" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#00cc66">
      使用技巧
    </text>
    
    <g transform="translate(80, 70)">
      <g transform="translate(0, 0)">
        <rect x="0" y="0" width="540" height="110" rx="15" fill="#e6fff0" stroke="#00cc66" stroke-width="2"/>
        <text x="270" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
          根据沟通对象调整侧重
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 技术人员：突出F（特性）和A（优势）
        </text>
        <text x="20" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 决策领导：强调B（收益）和E（证据）
        </text>
        <text x="20" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 使用人员：重点讲解B（收益）和操作便利性
        </text>
      </g>
      
      <g transform="translate(580, 0)">
        <rect x="0" y="0" width="540" height="110" rx="15" fill="#f0f8ff" stroke="#0066cc" stroke-width="2"/>
        <text x="270" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          结合故事化、可视化呈现
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 用故事包装FABE，增强感染力
        </text>
        <text x="20" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 用图表展示数据，提升说服力
        </text>
        <text x="20" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 用演示验证功能，增强体验感</text>
      </g>
      
      <g transform="translate(1160, 0)">
        <rect x="0" y="0" width="540" height="110" rx="15" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
        <text x="270" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
          FABE话术模型卡应用
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 制作个人专属话术卡片
        </text>
        <text x="20" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 针对不同场景准备话术模板
        </text>
        <text x="20" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
          • 持续优化和更新话术内容</text>
      </g>
    </g>
  </g>
  
  <!-- 工具应用提示 -->
  <rect x="200" y="920" width="1520" height="80" rx="40" fill="#0066cc"/>
  <text x="960" y="950" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">
    工具应用
  </text>
  <text x="960" y="980" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
    展示《FABE话术模型卡》示例，鼓励学员训后制作自己的卡片
  </text>
</svg>
