<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    顾问式提问：SPIN法实战演练
  </text>
  
  <!-- SPIN快速回顾 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="120" rx="20" fill="#f0f7ff" stroke="#0066cc" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#0066cc">
      SPIN提问法快速回顾
    </text>
    <text x="860" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      S（现状）→ P（问题）→ I（影响）→ N（需求价值）
    </text>
  </g>
  
  <!-- SPIN四类问题 -->
  <g transform="translate(50, 340)">
    <rect x="0" y="0" width="1820" height="180" rx="20" fill="#fafcff" stroke="#66ccff" stroke-width="3"/>
    <text x="910" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">
      四类问题的目的和逻辑
    </text>
    
    <g transform="translate(80, 70)">
      <rect x="0" y="0" width="420" height="90" rx="10" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
      <text x="210" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#0066cc">
        S - 现状问题 (Situation)
      </text>
      <text x="210" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        了解客户当前状况
      </text>
      <text x="210" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#666666">
        "目前使用什么设备？"
      </text>
      
      <rect x="440" y="0" width="420" height="90" rx="10" fill="#fff0f0" stroke="#ff6666" stroke-width="2"/>
      <text x="650" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#ff6666">
        P - 问题挖掘 (Problem)
      </text>
      <text x="650" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        发现困难和不满
      </text>
      <text x="650" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#666666">
        "遇到什么困难？"
      </text>
      
      <rect x="880" y="0" width="420" height="90" rx="10" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="1090" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#ff9900">
        I - 影响分析 (Implication)
      </text>
      <text x="1090" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        探讨问题的严重后果
      </text>
      <text x="1090" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#666666">
        "这会造成什么影响？"
      </text>
      
      <rect x="1320" y="0" width="420" height="90" rx="10" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="1530" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#00cc66">
        N - 需求价值 (Need-payoff)
      </text>
      <text x="1530" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        引导客户认识解决价值
      </text>
      <text x="1530" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#666666">
        "解决后有什么好处？"
      </text>
    </g>
  </g>
  
  <!-- 分组角色扮演练习 -->
  <g transform="translate(100, 560)">
    <rect x="0" y="0" width="1720" height="380" rx="20" fill="#f5fafa" stroke="#669999" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#006666">
      分组角色扮演练习
    </text>
    
    <!-- 场景1 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="800" height="280" rx="15" fill="#e6f3f3" stroke="#006666" stroke-width="2"/>
      <text x="400" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#006666">
        场景1：执法记录仪问题
      </text>
      
      <rect x="20" y="50" width="760" height="60" rx="10" fill="#cce6e6" stroke="#006666" stroke-width="1"/>
      <text x="400" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
        客户抱怨
      </text>
      <text x="400" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        "现有执法记录仪电池不耐用，内存总满"
      </text>
      
      <g transform="translate(20, 130)">
        <text x="0" y="25" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#006666">
          练习任务：
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 运用SPIN提问法深度挖掘
        </text>
        <text x="20" y="75" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 从表面问题挖掘到深层需求
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 引导客户认识解决方案价值
        </text>
        <text x="20" y="125" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 重点练习I（影响）问题的深度挖掘
        </text>
      </g>
    </g>
    
    <!-- 场景2 -->
    <g transform="translate(870, 80)">
      <rect x="0" y="0" width="800" height="280" rx="15" fill="#f0f8ff" stroke="#0066cc" stroke-width="2"/>
      <text x="400" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#0066cc">
        场景2：电信诈骗防范问题
      </text>
      
      <rect x="20" y="50" width="760" height="60" rx="10" fill="#cce6ff" stroke="#0066cc" stroke-width="1"/>
      <text x="400" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
        客户提及
      </text>
      <text x="400" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        "辖区电信诈骗高发，宣传效果不理想"
      </text>
      
      <g transform="translate(20, 130)">
        <text x="0" y="25" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          练习任务：
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 完整的SPIN提问流程演练
        </text>
        <text x="20" y="75" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 挖掘宣传效果差的深层原因
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 探讨对社区安全的影响
        </text>
        <text x="20" y="125" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 引导智慧宣传解决方案需求
        </text>
      </g>
    </g>
  </g>
  
  <!-- 练习安排 -->
  <g transform="translate(200, 980)">
    <rect x="0" y="0" width="1520" height="80" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">
      练习时间安排
    </text>
    <text x="760" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#0066cc">
      20-25分钟练习 + 10分钟代表分享与点评
    </text>
  </g>
</svg>
