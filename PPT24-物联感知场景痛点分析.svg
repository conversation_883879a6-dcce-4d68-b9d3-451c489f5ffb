<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6600;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc5500;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iotGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff9966;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc6633;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    场景深潜：物联感知 - 痛点分析
  </text>
  
  <!-- 现状描述 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="100" rx="20" fill="#fff5f0" stroke="#ff6600" stroke-width="3"/>
    <text x="860" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ff6600">
      物联感知现状
    </text>
    <text x="860" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#333333">
      物联网设备快速增长，但标准不统一、管理分散、数据孤岛等问题突出
    </text>
  </g>
  
  <!-- 核心痛点 -->
  <g transform="translate(100, 320)">
    <rect x="0" y="0" width="1720" height="500" rx="20" fill="#fffaf5" stroke="#ff9966" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ff6600">
      核心痛点分析
    </text>
    
    <!-- 痛点1：设备痛点 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6d9" stroke="#ff6600" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">
        设备痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 设备标准不统一
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        协议多样，互通困难
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 设备质量参差不齐
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        故障率高，维护困难
      </text>
    </g>
    
    <!-- 痛点2：网络痛点 -->
    <g transform="translate(480, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6d9" stroke="#ff6600" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">
        网络痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 网络制式多样
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        2G/3G/4G/NB-IoT混用
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 覆盖不均匀
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        偏远地区信号弱
      </text>
    </g>
    
    <!-- 痛点3：平台痛点 -->
    <g transform="translate(910, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6d9" stroke="#ff6600" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">
        平台痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 平台分散建设
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        各自为政，重复投资
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 数据孤岛严重
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        无法统一管理分析
      </text>
    </g>
    
    <!-- 痛点4：应用痛点 -->
    <g transform="translate(1340, 80)">
      <rect x="0" y="0" width="330" height="180" rx="15" fill="#ffe6d9" stroke="#ff6600" stroke-width="2"/>
      <text x="165" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">
        应用痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 智能化程度低
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        缺乏AI分析能力
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 预警能力弱
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        被动响应为主
      </text>
    </g>
    
    <!-- 具体场景痛点 -->
    <g transform="translate(50, 290)">
      <rect x="0" y="0" width="1620" height="180" rx="15" fill="url(#iotGradient)" opacity="0.1"/>
      <text x="810" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff6600">
        具体应用场景痛点
      </text>
      
      <g transform="translate(50, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          环境监测场景：
        </text>
        <text x="150" y="0" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          传感器精度不高、数据传输不稳定、预警阈值设置困难
        </text>
        
        <text x="0" y="35" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          门禁管控场景：
        </text>
        <text x="150" y="35" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          设备兼容性差、权限管理复杂、故障响应慢
        </text>
        
        <text x="0" y="70" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          车辆管理场景：
        </text>
        <text x="150" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          RFID读取距离短、车辆轨迹不连续、停车管理混乱
        </text>
        
        <text x="0" y="105" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          消防安全场景：
        </text>
        <text x="150" y="105" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          烟感误报率高、消防设备状态监控不及时、应急响应慢
        </text>
      </g>
    </g>
  </g>
  
  <!-- 客户需求总结 -->
  <g transform="translate(200, 860)">
    <rect x="0" y="0" width="1520" height="120" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#333333">
      客户核心需求
    </text>
    
    <g transform="translate(100, 70)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">
        技术需求：
      </text>
      <text x="120" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        统一标准、稳定网络、智能分析、预警联动
      </text>
      
      <text x="700" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        管理需求：
      </text>
      <text x="820" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        统一平台、集中管控、降低成本、提升效率
      </text>
    </g>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="1000" width="1320" height="60" rx="30" fill="#ff6600"/>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="white">
    物联感知需要NB-IoT网络+统一平台+AI分析的一体化物联网解决方案
  </text>
</svg>
