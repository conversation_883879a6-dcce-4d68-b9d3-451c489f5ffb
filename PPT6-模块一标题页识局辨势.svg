<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#001133;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#003366;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0066cc;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="lightGradient" cx="50%" cy="30%" r="60%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 光效 -->
  <ellipse cx="960" cy="300" rx="800" ry="400" fill="url(#lightGradient)"/>
  
  <!-- 灯塔图标 -->
  <g transform="translate(860, 200)">
    <!-- 灯塔主体 -->
    <polygon points="100,300 80,100 120,100" fill="#ffffff" opacity="0.9"/>
    <!-- 灯塔顶部 -->
    <rect x="85" y="80" width="30" height="30" fill="#ffff00" opacity="0.8"/>
    <!-- 光束 -->
    <path d="M100,95 L50,50 L150,50 Z" fill="#ffff00" opacity="0.3"/>
    <path d="M100,95 L30,150 L170,150 Z" fill="#ffff00" opacity="0.2"/>
    <!-- 灯塔条纹 -->
    <rect x="80" y="150" width="40" height="20" fill="#ff0000" opacity="0.7"/>
    <rect x="80" y="200" width="40" height="20" fill="#ff0000" opacity="0.7"/>
    <rect x="80" y="250" width="40" height="20" fill="#ff0000" opacity="0.7"/>
  </g>
  
  <!-- 航海图装饰 -->
  <g transform="translate(200, 600)" opacity="0.3">
    <circle cx="0" cy="0" r="3" fill="white"/>
    <circle cx="100" cy="50" r="3" fill="white"/>
    <circle cx="200" cy="-20" r="3" fill="white"/>
    <line x1="0" y1="0" x2="100" y2="50" stroke="white" stroke-width="1"/>
    <line x1="100" y1="50" x2="200" y2="-20" stroke="white" stroke-width="1"/>
  </g>
  
  <g transform="translate(1500, 700)" opacity="0.3">
    <circle cx="0" cy="0" r="3" fill="white"/>
    <circle cx="-80" cy="30" r="3" fill="white"/>
    <circle cx="-150" cy="-10" r="3" fill="white"/>
    <line x1="0" y1="0" x2="-80" y2="30" stroke="white" stroke-width="1"/>
    <line x1="-80" y1="30" x2="-150" y2="-10" stroke="white" stroke-width="1"/>
  </g>
  
  <!-- 主标题 -->
  <text x="960" y="500" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="white">
    模块一：识局辨势
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="580" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#ccddff">
    把握智慧警务政策市场脉搏
  </text>
  
  <!-- 引导语 -->
  <rect x="560" y="650" width="800" height="100" rx="50" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
  <text x="960" y="710" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="white">
    "看清大势，才能找准方向。
  </text>
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="white">
    我们先来解读政策，洞察市场。"
  </text>
  
  <!-- 装饰性波浪 -->
  <path d="M0,950 Q480,920 960,950 T1920,950" stroke="white" stroke-width="2" fill="none" opacity="0.5"/>
  <path d="M0,980 Q480,950 960,980 T1920,980" stroke="white" stroke-width="2" fill="none" opacity="0.3"/>
</svg>
