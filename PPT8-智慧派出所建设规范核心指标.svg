<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="layerGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e6f3ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cce7ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="layerGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f0fff0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0ffe0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    操作手册：《智慧派出所建设规范》（最新版）核心指标
  </text>
  
  <!-- 感知层 -->
  <g transform="translate(100, 160)">
    <rect x="0" y="0" width="350" height="180" rx="15" fill="url(#layerGradient1)" stroke="#0066cc" stroke-width="2"/>
    <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">
      感知层
    </text>
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 视频高清/AI覆盖率
    </text>
    <text x="20" y="105" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 物联设备种类与联网率
    </text>
    <text x="20" y="140" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      （摄像头、传感器、门禁等）
    </text>
  </g>
  
  <!-- 网络层 -->
  <g transform="translate(500, 160)">
    <rect x="0" y="0" width="350" height="180" rx="15" fill="url(#layerGradient2)" stroke="#00cc66" stroke-width="2"/>
    <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#00cc66">
      网络层
    </text>
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 网络带宽要求
    </text>
    <text x="20" y="105" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 移动警务网络要求
    </text>
    <text x="20" y="140" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      （5G、光纤、专网等）
    </text>
  </g>
  
  <!-- 平台层 -->
  <g transform="translate(900, 160)">
    <rect x="0" y="0" width="350" height="180" rx="15" fill="url(#layerGradient1)" stroke="#ff9900" stroke-width="2"/>
    <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff9900">
      平台层
    </text>
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 数据汇聚共享要求
    </text>
    <text x="20" y="105" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • AI分析能力要求
    </text>
    <text x="20" y="140" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      （云平台、大数据、AI算法）
    </text>
  </g>
  
  <!-- 应用层 -->
  <g transform="translate(1300, 160)">
    <rect x="0" y="0" width="350" height="180" rx="15" fill="url(#layerGradient2)" stroke="#cc0066" stroke-width="2"/>
    <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#cc0066">
      应用层
    </text>
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 智能巡防功能要求
    </text>
    <text x="20" y="105" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 智能服务功能要求
    </text>
    <text x="20" y="140" font-family="Microsoft YaHei" font-size="22" fill="#333333">
      • 智能管理功能要求
    </text>
  </g>
  
  <!-- 安全层 -->
  <g transform="translate(700, 380)">
    <rect x="0" y="0" width="520" height="200" rx="15" fill="#fff0f0" stroke="#ff0000" stroke-width="3"/>
    <text x="260" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ff0000">
      安全层（重中之重）
    </text>
    <text x="40" y="90" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#333333">
      • 等保三级要求
    </text>
    <text x="40" y="130" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="#333333">
      • 视频/数据安全要求（GB35114）
    </text>
    <text x="40" y="170" font-family="Microsoft YaHei" font-size="22" fill="#666666">
      （网络安全、数据安全、隐私保护）
    </text>
  </g>
  
  <!-- 连接线 -->
  <g stroke="#666666" stroke-width="2" fill="none" opacity="0.5">
    <line x1="275" y1="340" x2="275" y2="380"/>
    <line x1="675" y1="340" x2="675" y2="380"/>
    <line x1="1075" y1="340" x2="1075" y2="380"/>
    <line x1="1475" y1="340" x2="1475" y2="380"/>
    
    <line x1="275" y1="380" x2="700" y2="380"/>
    <line x1="675" y1="380" x2="700" y2="380"/>
    <line x1="1075" y1="380" x2="1220" y2="380"/>
    <line x1="1475" y1="380" x2="1220" y2="380"/>
  </g>
  
  <!-- 重要提示 -->
  <rect x="200" y="650" width="1520" height="120" rx="20" fill="#f8f9fa" stroke="#0066cc" stroke-width="2"/>
  <text x="960" y="690" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">
    这是方案设计的"标尺"
  </text>
  <text x="960" y="730" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" fill="#333333">
    要逐项对照，确保方案符合规范要求，并关注本地落实情况
  </text>
  
  <!-- 电信优势提示 -->
  <rect x="400" y="820" width="1120" height="80" rx="40" fill="#0066cc"/>
  <text x="960" y="870" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
    电信优势：云网融合 + 安全可信 + 一站式服务，完美匹配规范要求！
  </text>
</svg>
