<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="levelGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#66ccff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3399cc;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="levelGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff9900;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc7700;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="levelGradient3" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00cc66;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#009944;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    需求进阶：从"听需求"到"挖需求"、"创需求"
  </text>
  
  <!-- 方法论框架 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="120" rx="20" fill="#f0f7ff" stroke="#0066cc" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#0066cc">
      需求挖掘三层次方法论
    </text>
    <text x="860" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      听需求（基础） → 挖需求（关键） → 创需求（进阶）
    </text>
  </g>
  
  <!-- 三个层次详解 -->
  <g transform="translate(50, 340)">
    <!-- 听需求 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="540" height="320" rx="20" fill="#e6f3ff" stroke="#66ccff" stroke-width="3"/>
      <rect x="0" y="0" width="540" height="80" rx="20" fill="url(#levelGradient1)"/>
      <text x="270" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">
        听需求（基础）
      </text>
      
      <g transform="translate(30, 100)">
        <text x="0" y="30" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">
          定义：
        </text>
        <text x="100" y="30" font-family="Microsoft YaHei" font-size="26" fill="#333333">
          客户明确表达的需求
        </text>
        
        <text x="0" y="80" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">
          特点：
        </text>
        <text x="30" y="115" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 表面化、直接化
        </text>
        <text x="30" y="145" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 往往是解决方案而非问题
        </text>
        <text x="30" y="175" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 容易陷入价格竞争
        </text>
        
        <rect x="0" y="200" width="480" height="80" rx="10" fill="#cce6ff" stroke="#0066cc" stroke-width="2"/>
        <text x="240" y="225" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#0066cc">
          典型例子
        </text>
        <text x="240" y="255" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          "我们要加摄像头"
        </text>
      </g>
    </g>
    
    <!-- 挖需求 -->
    <g transform="translate(580, 0)">
      <rect x="0" y="0" width="540" height="320" rx="20" fill="#fff8f0" stroke="#ffbb66" stroke-width="3"/>
      <rect x="0" y="0" width="540" height="80" rx="20" fill="url(#levelGradient2)"/>
      <text x="270" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">
        挖需求（关键）
      </text>
      
      <g transform="translate(30, 100)">
        <text x="0" y="30" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff9900">
          定义：
        </text>
        <text x="100" y="30" font-family="Microsoft YaHei" font-size="26" fill="#333333">
          通过提问分析找到深层原因
        </text>
        
        <text x="0" y="80" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff9900">
          方法：
        </text>
        <text x="30" y="115" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 连续追问"为什么"
        </text>
        <text x="30" y="145" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 分析业务流程痛点
        </text>
        <text x="30" y="175" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 挖掘根本问题
        </text>
        
        <rect x="0" y="200" width="480" height="80" rx="10" fill="#ffe6cc" stroke="#ff9900" stroke-width="2"/>
        <text x="240" y="225" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#ff9900">
          深层挖掘
        </text>
        <text x="240" y="255" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          "为何加？因为破案率低"
        </text>
      </g>
    </g>
    
    <!-- 创需求 -->
    <g transform="translate(1160, 0)">
      <rect x="0" y="0" width="540" height="320" rx="20" fill="#f0fff0" stroke="#66cc99" stroke-width="3"/>
      <rect x="0" y="0" width="540" height="80" rx="20" fill="url(#levelGradient3)"/>
      <text x="270" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="white">
        创需求（进阶）
      </text>
      
      <g transform="translate(30, 100)">
        <text x="0" y="30" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#00cc66">
          定义：
        </text>
        <text x="100" y="30" font-family="Microsoft YaHei" font-size="26" fill="#333333">
          主动提出价值建议
        </text>
        
        <text x="0" y="80" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#00cc66">
          能力：
        </text>
        <text x="30" y="115" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 洞察潜在需求
        </text>
        <text x="30" y="145" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 提供创新方案
        </text>
        <text x="30" y="175" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          • 引领客户思维
        </text>
        
        <rect x="0" y="200" width="480" height="80" rx="10" fill="#e6ffe6" stroke="#00cc66" stroke-width="2"/>
        <text x="240" y="225" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#00cc66">
          价值创新
        </text>
        <text x="240" y="255" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#333333">
          "加AI摄像头做预测预警"
        </text>
      </g>
    </g>
  </g>
  
  <!-- 进阶路径 -->
  <g transform="translate(100, 700)">
    <rect x="0" y="0" width="1720" height="200" rx="20" fill="#fafcff" stroke="#66ccff" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">
      顾问式销售进阶路径
    </text>
    
    <!-- 箭头和连接线 -->
    <path d="M 300 100 Q 400 80 500 100" stroke="#0066cc" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
    <path d="M 800 100 Q 900 80 1000 100" stroke="#0066cc" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
    
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="200" height="100" rx="15" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
      <text x="100" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
        被动响应
      </text>
      <text x="100" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        满足明确需求
      </text>
      <text x="100" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        价格竞争
      </text>
    </g>
    
    <g transform="translate(350, 80)">
      <rect x="0" y="0" width="200" height="100" rx="15" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="100" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
        主动挖掘
      </text>
      <text x="100" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        发现真实痛点
      </text>
      <text x="100" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        价值竞争
      </text>
    </g>
    
    <g transform="translate(650, 80)">
      <rect x="0" y="0" width="200" height="100" rx="15" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="100" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
        引领创新
      </text>
      <text x="100" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        创造新价值
      </text>
      <text x="100" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#333333">
        差异化优势
      </text>
    </g>
    
    <g transform="translate(1200, 80)">
      <rect x="0" y="0" width="470" height="100" rx="15" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
      <text x="235" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">
        核心能力要求
      </text>
      <text x="235" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#0066cc">
        具备"挖"和"创"的能力
      </text>
      <text x="235" y="85" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#666666">
        成为客户的顾问和伙伴
      </text>
    </g>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="940" width="1320" height="80" rx="40" fill="#0066cc"/>
  <text x="960" y="970" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
    顾问式销售的核心：
  </text>
  <text x="960" y="1005" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
    从"听需求"进阶到"挖需求"、"创需求"
  </text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc"/>
    </marker>
  </defs>
</svg>
