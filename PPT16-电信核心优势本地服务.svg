<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="locationGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00cc66;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#009944;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    亮剑时刻：本地服务 - 扎根属地，快速响应
  </text>
  
  <!-- 服务网络覆盖图 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="800" height="400" rx="20" fill="#f0fff0" stroke="#00cc66" stroke-width="3"/>
    <text x="400" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#00cc66">
      全覆盖服务网络
    </text>
    
    <!-- 服务层级 -->
    <g transform="translate(100, 80)">
      <!-- 省级 -->
      <circle cx="300" cy="80" r="50" fill="url(#locationGradient)"/>
      <text x="300" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">省级</text>
      <text x="300" y="95" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="white">总部</text>
      
      <!-- 市级 -->
      <g transform="translate(-80, 120)">
        <circle cx="200" cy="60" r="35" fill="url(#locationGradient)"/>
        <text x="200" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">市级</text>
        <text x="200" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">分公司</text>
      </g>
      
      <g transform="translate(80, 120)">
        <circle cx="200" cy="60" r="35" fill="url(#locationGradient)"/>
        <text x="200" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">市级</text>
        <text x="200" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">分公司</text>
      </g>
      
      <g transform="translate(240, 120)">
        <circle cx="200" cy="60" r="35" fill="url(#locationGradient)"/>
        <text x="200" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">市级</text>
        <text x="200" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">分公司</text>
      </g>
      
      <!-- 县级 -->
      <g transform="translate(-120, 220)">
        <circle cx="80" cy="40" r="25" fill="url(#locationGradient)"/>
        <text x="80" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">县级</text>
        <text x="80" y="52" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">营业部</text>
        
        <circle cx="160" cy="40" r="25" fill="url(#locationGradient)"/>
        <text x="160" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">县级</text>
        <text x="160" y="52" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">营业部</text>
      </g>
      
      <g transform="translate(200, 220)">
        <circle cx="80" cy="40" r="25" fill="url(#locationGradient)"/>
        <text x="80" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">县级</text>
        <text x="80" y="52" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">营业部</text>
        
        <circle cx="160" cy="40" r="25" fill="url(#locationGradient)"/>
        <text x="160" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">县级</text>
        <text x="160" y="52" text-anchor="middle" font-family="Microsoft YaHei" font-size="10" fill="white">营业部</text>
      </g>
      
      <!-- 连接线 -->
      <g stroke="#00cc66" stroke-width="2" fill="none">
        <line x1="250" y1="130" x2="120" y2="180"/>
        <line x1="300" y1="130" x2="280" y2="180"/>
        <line x1="350" y1="130" x2="440" y2="180"/>
        
        <line x1="120" y1="240" x2="100" y2="260"/>
        <line x1="120" y1="240" x2="140" y2="260"/>
        <line x1="280" y1="240" x2="260" y2="260"/>
        <line x1="280" y1="240" x2="300" y2="260"/>
        <line x1="440" y1="240" x2="420" y2="260"/>
        <line x1="440" y1="240" x2="460" y2="260"/>
      </g>
    </g>
    
    <text x="400" y="350" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">
      省市县乡四级服务体系
    </text>
    <text x="400" y="380" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      就近服务，快速响应
    </text>
  </g>
  
  <!-- 铁三角服务模式 -->
  <g transform="translate(1020, 180)">
    <rect x="0" y="0" width="800" height="400" rx="20" fill="#f0f7ff" stroke="#0066cc" stroke-width="3"/>
    <text x="400" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#0066cc">
      "铁三角"服务模式
    </text>
    
    <!-- 三角形布局 -->
    <g transform="translate(400, 120)">
      <!-- 客户经理 -->
      <circle cx="0" cy="-80" r="50" fill="#0066cc"/>
      <text x="0" y="-85" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">客户经理</text>
      <text x="0" y="-70" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="white">营销拓展</text>
      
      <!-- 解决方案经理 -->
      <circle cx="-100" cy="60" r="50" fill="#00cc66"/>
      <text x="-100" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">解决方案</text>
      <text x="-100" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">经理</text>
      <text x="-100" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="white">技术支撑</text>
      
      <!-- 交付经理 -->
      <circle cx="100" cy="60" r="50" fill="#ff9900"/>
      <text x="100" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">交付经理</text>
      <text x="100" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="white">项目实施</text>
      
      <!-- 连接线 -->
      <g stroke="#666666" stroke-width="3" fill="none">
        <line x1="-35" y1="-45" x2="-65" y2="25"/>
        <line x1="35" y1="-45" x2="65" y2="25"/>
        <line x1="-50" y1="60" x2="50" y2="60"/>
      </g>
      
      <!-- 中心客户 -->
      <circle cx="0" cy="0" r="30" fill="#cc0066"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">客户</text>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="white">需求</text>
    </g>
    
    <text x="400" y="320" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">
      专业分工，协同作战
    </text>
    <text x="400" y="350" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      围绕客户需求，提供全方位服务
    </text>
  </g>
  
  <!-- 本地服务优势 -->
  <g transform="translate(200, 640)">
    <rect x="0" y="0" width="1520" height="280" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#333333">
      本地服务核心优势
    </text>
    
    <!-- 优势1：响应速度 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="350" height="160" rx="15" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
      <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        响应速度快
      </text>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 就近服务，2小时响应
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 本地团队，熟悉环境
      </text>
      <text x="20" y="130" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 7×24小时保障
      </text>
    </g>
    
    <!-- 优势2：关系深厚 -->
    <g transform="translate(430, 80)">
      <rect x="0" y="0" width="350" height="160" rx="15" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
        客户关系深
      </text>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 长期合作伙伴
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 深度了解需求
      </text>
      <text x="20" y="130" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 信任基础牢固
      </text>
    </g>
    
    <!-- 优势3：成本优势 -->
    <g transform="translate(810, 80)">
      <rect x="0" y="0" width="350" height="160" rx="15" fill="#fff5f0" stroke="#ff9900" stroke-width="2"/>
      <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        服务成本低
      </text>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 减少差旅成本
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 提高服务效率
      </text>
      <text x="20" y="130" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 降低沟通成本
      </text>
    </g>
    
    <!-- 优势4：持续服务 -->
    <g transform="translate(1190, 80)">
      <rect x="0" y="0" width="280" height="160" rx="15" fill="#fff0f5" stroke="#cc0066" stroke-width="2"/>
      <text x="140" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#cc0066">
        持续服务强
      </text>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 全生命周期
      </text>
      <text x="20" y="100" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 持续优化
      </text>
      <text x="20" y="130" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        • 长期伙伴
      </text>
    </g>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="960" width="1320" height="80" rx="40" fill="#00cc66"/>
  <text x="960" y="1010" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
    本地服务是电信独特优势，扎根属地，快速响应，深度服务客户
  </text>
</svg>
