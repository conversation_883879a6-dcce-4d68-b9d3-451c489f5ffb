<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#cc0066;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#990044;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    场景2：电信诈骗防范SPIN演练示范
  </text>
  
  <!-- 场景2示范 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="380" rx="20" fill="#fff0f8" stroke="#cc0066" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#cc0066">
      场景2示范：电信诈骗宣传效果问题深度挖掘
    </text>
    
    <g transform="translate(50, 80)">
      <!-- S问题示范 -->
      <rect x="0" y="0" width="400" height="120" rx="10" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
      <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
        S - 现状问题
      </text>
      <text x="20" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "目前采用哪些宣传方式？"
      </text>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "宣传频率是怎样的？"
      </text>
      <text x="20" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "覆盖了多少居民？"
      </text>
      <text x="20" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "辖区诈骗案件现状如何？"
      </text>
      
      <!-- P问题示范 -->
      <rect x="420" y="0" width="400" height="120" rx="10" fill="#fff0f0" stroke="#ff6666" stroke-width="2"/>
      <text x="620" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6666">
        P - 问题挖掘
      </text>
      <text x="440" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "宣传效果不理想体现在哪里？"
      </text>
      <text x="440" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "居民参与度如何？"
      </text>
      <text x="440" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "哪些人群最难触达？"
      </text>
      <text x="440" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "宣传内容有什么问题？"
      </text>
      
      <!-- I问题示范 -->
      <rect x="840" y="0" width="400" height="120" rx="10" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="1040" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
        I - 影响分析（重点）
      </text>
      <text x="860" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "宣传不到位会导致什么后果？"
      </text>
      <text x="860" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "对辖区安全有什么影响？"
      </text>
      <text x="860" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "会影响派出所的工作压力吗？"
      </text>
      <text x="860" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "对居民信任度有什么影响？"
      </text>
      
      <!-- N问题示范 -->
      <rect x="1260" y="0" width="400" height="120" rx="10" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="1460" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
        N - 需求价值
      </text>
      <text x="1280" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "智能化宣传有什么好处？"
      </text>
      <text x="1280" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "精准推送能提升效果吗？"
      </text>
      <text x="1280" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "互动式宣传的价值在哪里？"
      </text>
      <text x="1280" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "这样能降低案发率吗？"
      </text>
    </g>
    
    <!-- 挖掘路径 -->
    <g transform="translate(50, 220)">
      <rect x="0" y="0" width="1620" height="120" rx="15" fill="#cc0066" opacity="0.1"/>
      <text x="810" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#cc0066">
        深度挖掘路径示例
      </text>
      
      <g transform="translate(50, 50)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#333333">
          表面问题：
        </text>
        <text x="120" y="0" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          宣传效果不理想
        </text>
        
        <text x="0" y="30" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#333333">
          深层影响：
        </text>
        <text x="120" y="30" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          居民防范意识低 → 诈骗案件高发 → 社区安全隐患 → 警民关系紧张
        </text>
        
        <text x="0" y="60" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#333333">
          解决价值：
        </text>
        <text x="120" y="60" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          精准宣传 → 提升防范意识 → 降低案发率 → 提升社区安全感
        </text>
      </g>
    </g>
  </g>
  
  <!-- 智慧宣传解决方案引导 -->
  <g transform="translate(100, 600)">
    <rect x="0" y="0" width="1720" height="300" rx="20" fill="#f0f8ff" stroke="#0066cc" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">
      智慧宣传解决方案需求引导
    </text>
    
    <g transform="translate(50, 80)">
      <!-- 痛点分析 -->
      <rect x="0" y="0" width="520" height="180" rx="15" fill="#fff0f0" stroke="#ff6666" stroke-width="2"/>
      <text x="260" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        传统宣传痛点
      </text>
      
      <g transform="translate(20, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          覆盖面有限：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 传统媒体触达率低
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 老年人群难以覆盖
        </text>
        
        <text x="0" y="80" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          效果难评估：
        </text>
        <text x="20" y="105" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 无法量化宣传效果
        </text>
        <text x="20" y="130" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 缺乏反馈机制
        </text>
      </g>
      
      <!-- 解决方案价值 -->
      <rect x="540" y="0" width="520" height="180" rx="15" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="800" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
        智慧宣传价值
      </text>
      
      <g transform="translate(560, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          精准触达：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 多渠道智能推送
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 个性化内容定制
        </text>
        
        <text x="0" y="80" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          效果可视：
        </text>
        <text x="20" y="105" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 实时数据监控
        </text>
        <text x="20" y="130" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 效果量化分析
        </text>
      </g>
      
      <!-- 技术实现 -->
      <rect x="1080" y="0" width="520" height="180" rx="15" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="1340" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        技术实现路径
      </text>
      
      <g transform="translate(1100, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          智能平台：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 大数据分析平台
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • AI内容生成
        </text>
        
        <text x="0" y="80" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          多端触达：
        </text>
        <text x="20" y="105" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 短信、APP、小程序
        </text>
        <text x="20" y="130" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 社区大屏、广播
        </text>
      </g>
    </g>
  </g>
  
  <!-- 练习要点提醒 -->
  <rect x="200" y="940" width="1520" height="120" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
  <text x="960" y="970" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#333333">
    场景2练习要点
  </text>
  <text x="960" y="1000" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#cc0066">
    重点挖掘：宣传效果差对社区安全和警民关系的深层影响
  </text>
  <text x="960" y="1030" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#666666">
    引导方向：智慧宣传平台的精准触达和效果量化价值
  </text>
</svg>
