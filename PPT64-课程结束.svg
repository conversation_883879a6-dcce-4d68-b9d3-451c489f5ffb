<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="backgroundGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f8ff;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#003366;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#003366;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="subtitleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#cc6600;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff9900;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc6600;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#backgroundGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M 0 200 Q 480 150 960 200 Q 1440 250 1920 200" stroke="#0066cc" stroke-width="6" fill="none" opacity="0.3"/>
  <path d="M 0 250 Q 480 200 960 250 Q 1440 300 1920 250" stroke="#ff9900" stroke-width="4" fill="none" opacity="0.3"/>
  <path d="M 0 300 Q 480 250 960 300 Q 1440 350 1920 300" stroke="#00cc66" stroke-width="4" fill="none" opacity="0.3"/>
  
  <!-- 主标题 -->
  <g transform="translate(960, 400)">
    <rect x="-600" y="-80" width="1200" height="160" rx="80" fill="url(#titleGradient)" opacity="0.9"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="white">
      智勇双全·赢在警务
    </text>
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">
      中国电信智慧派出所实战营销与方案赋能
    </text>
  </g>
  
  <!-- 副标题 -->
  <g transform="translate(960, 580)">
    <rect x="-400" y="-40" width="800" height="80" rx="40" fill="url(#subtitleGradient)" opacity="0.9"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">
      课程圆满结束
    </text>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 720)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#003366">
      学以致用 • 实战为王 • 持续精进 • 共创辉煌
    </text>
  </g>
  
  <!-- 联系信息 -->
  <g transform="translate(960, 820)">
    <rect x="-500" y="-60" width="1000" height="120" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2" opacity="0.9"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">
      培训支持与联系方式
    </text>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      中国电信智慧派出所培训中心
    </text>
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      持续为您提供专业支持与服务
    </text>
  </g>
  
  <!-- 装饰性元素 -->
  <g transform="translate(200, 950)">
    <circle cx="0" cy="0" r="15" fill="#0066cc" opacity="0.6"/>
    <circle cx="40" cy="0" r="12" fill="#ff9900" opacity="0.6"/>
    <circle cx="75" cy="0" r="10" fill="#00cc66" opacity="0.6"/>
    <circle cx="105" cy="0" r="8" fill="#cc6600" opacity="0.6"/>
  </g>
  
  <g transform="translate(1520, 950)">
    <circle cx="0" cy="0" r="8" fill="#cc6600" opacity="0.6"/>
    <circle cx="35" cy="0" r="10" fill="#00cc66" opacity="0.6"/>
    <circle cx="70" cy="0" r="12" fill="#ff9900" opacity="0.6"/>
    <circle cx="110" cy="0" r="15" fill="#0066cc" opacity="0.6"/>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M 0 980 Q 480 930 960 980 Q 1440 1030 1920 980" stroke="#0066cc" stroke-width="6" fill="none" opacity="0.3"/>
  <path d="M 0 1020 Q 480 970 960 1020 Q 1440 1070 1920 1020" stroke="#ff9900" stroke-width="4" fill="none" opacity="0.3"/>
  
  <!-- 结束标识 -->
  <g transform="translate(960, 1000)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#003366">
      谢谢大家！
    </text>
  </g>
</svg>
