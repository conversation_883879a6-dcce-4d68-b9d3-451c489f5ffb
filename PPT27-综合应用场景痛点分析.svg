<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff9900;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc7700;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    场景深潜：综合应用 - 痛点分析
  </text>
  
  <!-- 现状描述 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="100" rx="20" fill="#fff8f0" stroke="#ff9900" stroke-width="3"/>
    <text x="860" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ff9900">
      综合应用现状
    </text>
    <text x="860" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#333333">
      各类警务应用系统众多，但缺乏统一规划，系统割裂、功能重复、用户体验差
    </text>
  </g>
  
  <!-- 核心痛点 -->
  <g transform="translate(100, 320)">
    <rect x="0" y="0" width="1720" height="500" rx="20" fill="#fffcf5" stroke="#ffbb66" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ff9900">
      核心痛点分析
    </text>
    
    <!-- 痛点1：系统痛点 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6cc" stroke="#ff9900" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        系统痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 系统众多分散
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        各自建设，缺乏统筹
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 功能重复建设
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        资源浪费，效率低下
      </text>
    </g>
    
    <!-- 痛点2：集成痛点 -->
    <g transform="translate(480, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6cc" stroke="#ff9900" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        集成痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 接口标准不统一
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        集成成本高，周期长
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 数据格式不一致
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        数据交换困难
      </text>
    </g>
    
    <!-- 痛点3：用户体验痛点 -->
    <g transform="translate(910, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6cc" stroke="#ff9900" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        用户体验痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 界面风格不统一
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        学习成本高，操作复杂
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 多系统切换频繁
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        工作效率低下
      </text>
    </g>
    
    <!-- 痛点4：维护痛点 -->
    <g transform="translate(1340, 80)">
      <rect x="0" y="0" width="330" height="180" rx="15" fill="#ffe6cc" stroke="#ff9900" stroke-width="2"/>
      <text x="165" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        维护痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 运维成本高
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        多套系统分别维护
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 升级困难
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        版本兼容性问题
      </text>
    </g>
    
    <!-- 具体场景痛点 -->
    <g transform="translate(50, 290)">
      <rect x="0" y="0" width="1620" height="180" rx="15" fill="#ff9900" opacity="0.1"/>
      <text x="810" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff9900">
        具体应用场景痛点
      </text>
      
      <g transform="translate(50, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          一站式服务大厅：
        </text>
        <text x="180" y="0" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          系统切换频繁、办事流程复杂、等待时间长
        </text>
        
        <text x="0" y="35" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          综合执法平台：
        </text>
        <text x="180" y="35" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          执法标准不统一、证据管理分散、流程不规范
        </text>
        
        <text x="0" y="70" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          智慧社区管理：
        </text>
        <text x="180" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          信息采集重复、服务功能单一、居民参与度低
        </text>
        
        <text x="0" y="105" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          移动办公平台：
        </text>
        <text x="180" y="105" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          功能不全面、同步不及时、离线能力弱
        </text>
      </g>
    </g>
  </g>
  
  <!-- 客户需求总结 -->
  <g transform="translate(200, 860)">
    <rect x="0" y="0" width="1520" height="120" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#333333">
      客户核心需求
    </text>
    
    <g transform="translate(100, 70)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        集成需求：
      </text>
      <text x="120" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        统一平台、标准接口、数据共享、流程整合
      </text>
      
      <text x="700" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        体验需求：
      </text>
      <text x="820" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        界面统一、操作简便、响应快速、功能完善
      </text>
    </g>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="1000" width="1320" height="60" rx="30" fill="#ff9900"/>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="white">
    综合应用需要统一门户+微服务架构+标准接口的一体化应用集成解决方案
  </text>
</svg>
