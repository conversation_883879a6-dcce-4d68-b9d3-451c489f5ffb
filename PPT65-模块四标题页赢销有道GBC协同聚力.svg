<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#006699;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004466;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f8ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e6f3ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 背景装饰弧线 -->
  <path d="M 0 300 Q 480 250 960 300 Q 1440 350 1920 300" stroke="#006699" stroke-width="6" fill="none" opacity="0.2"/>
  <path d="M 0 400 Q 480 350 960 400 Q 1440 450 1920 400" stroke="#0099cc" stroke-width="4" fill="none" opacity="0.2"/>
  <path d="M 0 500 Q 480 450 960 500 Q 1440 550 1920 500" stroke="#33aadd" stroke-width="3" fill="none" opacity="0.2"/>
  
  <!-- 主标题区域 -->
  <rect x="200" y="200" width="1520" height="200" rx="40" fill="url(#headerGradient)"/>
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="white">
    模块四：赢销有道
  </text>
  <text x="960" y="350" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">
    掌握沟通利器，联动GBC资源
  </text>
  
  <!-- 引导语 -->
  <g transform="translate(300, 480)">
    <rect x="0" y="0" width="1320" height="120" rx="30" fill="url(#cardGradient)" stroke="#006699" stroke-width="3"/>
    <text x="660" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#006699">
      引导语
    </text>
    <text x="660" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" font-weight="bold" fill="#333333">
      "好方案还需要会吆喝，更要懂得借力打力。"
    </text>
  </g>
  
  <!-- GBC协同示意图 -->
  <g transform="translate(960, 700)">
    <!-- G端 -->
    <g transform="translate(-300, 0)">
      <circle cx="0" cy="0" r="80" fill="#ff6666" opacity="0.9"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
        G端
      </text>
      <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">
        政府
      </text>
      <text x="0" y="130" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        政策引导
      </text>
      <text x="0" y="155" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        资金支持
      </text>
    </g>
    
    <!-- B端 -->
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="80" fill="#00cc66" opacity="0.9"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
        B端
      </text>
      <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">
        企业
      </text>
      <text x="0" y="130" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        场景应用
      </text>
      <text x="0" y="155" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        渠道合作
      </text>
    </g>
    
    <!-- C端 -->
    <g transform="translate(300, 0)">
      <circle cx="0" cy="0" r="80" fill="#3366cc" opacity="0.9"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
        C端
      </text>
      <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">
        个人
      </text>
      <text x="0" y="130" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        服务体验
      </text>
      <text x="0" y="155" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#333333">
        价值实现
      </text>
    </g>
    
    <!-- 连接线 -->
    <path d="M -220 0 L -80 0" stroke="#666666" stroke-width="4" marker-end="url(#arrowhead)"/>
    <path d="M 80 0 L 220 0" stroke="#666666" stroke-width="4" marker-end="url(#arrowhead)"/>
    <path d="M -150 -60 L 150 -60" stroke="#666666" stroke-width="3" stroke-dasharray="5,5"/>
    
    <!-- 中心协同 -->
    <rect x="-60" y="-40" width="120" height="80" rx="20" fill="#ff9900" opacity="0.9"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      协同
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      聚力
    </text>
  </g>
  
  <!-- 学习目标 -->
  <g transform="translate(200, 950)">
    <rect x="0" y="0" width="1520" height="100" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#006699">
      学习目标
    </text>
    <text x="760" y="65" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      掌握FABE价值呈现法 • 精通GBC协同营销策略 • 提升沟通说服能力
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666666"/>
    </marker>
  </defs>
</svg>
