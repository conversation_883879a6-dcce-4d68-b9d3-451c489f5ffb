<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9966cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#663399;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#bb99dd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8866aa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    场景深潜：数据治理 - 痛点分析
  </text>
  
  <!-- 现状描述 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="100" rx="20" fill="#f5f0ff" stroke="#9966cc" stroke-width="3"/>
    <text x="860" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#9966cc">
      数据治理现状
    </text>
    <text x="860" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#333333">
      数据量爆发式增长，但数据质量差、标准不统一、价值挖掘不足等问题严重
    </text>
  </g>
  
  <!-- 核心痛点 -->
  <g transform="translate(100, 320)">
    <rect x="0" y="0" width="1720" height="500" rx="20" fill="#faf5ff" stroke="#bb99dd" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#9966cc">
      核心痛点分析
    </text>
    
    <!-- 痛点1：数据质量痛点 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#f0e6ff" stroke="#9966cc" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#9966cc">
        数据质量痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 数据准确性差
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        错误数据、重复数据多
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 数据完整性不足
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        缺失字段、空值较多
      </text>
    </g>
    
    <!-- 痛点2：数据标准痛点 -->
    <g transform="translate(480, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#f0e6ff" stroke="#9966cc" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#9966cc">
        数据标准痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 标准不统一
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        各系统标准不一致
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 编码不规范
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        同一事物多种编码
      </text>
    </g>
    
    <!-- 痛点3：数据共享痛点 -->
    <g transform="translate(910, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#f0e6ff" stroke="#9966cc" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#9966cc">
        数据共享痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 部门壁垒严重
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        各自为政，不愿共享
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 技术壁垒高
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        接口不统一，对接困难
      </text>
    </g>
    
    <!-- 痛点4：数据应用痛点 -->
    <g transform="translate(1340, 80)">
      <rect x="0" y="0" width="330" height="180" rx="15" fill="#f0e6ff" stroke="#9966cc" stroke-width="2"/>
      <text x="165" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#9966cc">
        数据应用痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 分析能力弱
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        缺乏专业分析工具
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 价值挖掘不足
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        数据沉睡，价值未现
      </text>
    </g>
    
    <!-- 具体场景痛点 -->
    <g transform="translate(50, 290)">
      <rect x="0" y="0" width="1620" height="180" rx="15" fill="url(#dataGradient)" opacity="0.1"/>
      <text x="810" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#9966cc">
        具体应用场景痛点
      </text>
      
      <g transform="translate(50, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          人员信息管理：
        </text>
        <text x="150" y="0" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          身份信息不一致、重复录入、更新不及时、查询效率低
        </text>
        
        <text x="0" y="35" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          案件信息管理：
        </text>
        <text x="150" y="35" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          案件分类不统一、关联分析困难、统计报表不准确
        </text>
        
        <text x="0" y="70" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          视频数据管理：
        </text>
        <text x="150" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          存储分散、检索困难、结构化程度低、智能分析不足
        </text>
        
        <text x="0" y="105" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          综合研判分析：
        </text>
        <text x="150" y="105" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          数据孤岛严重、关联分析能力弱、预测预警不准确
        </text>
      </g>
    </g>
  </g>
  
  <!-- 客户需求总结 -->
  <g transform="translate(200, 860)">
    <rect x="0" y="0" width="1520" height="120" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#333333">
      客户核心需求
    </text>
    
    <g transform="translate(100, 70)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#9966cc">
        治理需求：
      </text>
      <text x="120" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        数据标准化、质量提升、统一管理、安全保障
      </text>
      
      <text x="700" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        应用需求：
      </text>
      <text x="820" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        智能分析、价值挖掘、辅助决策、预测预警
      </text>
    </g>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="1000" width="1320" height="60" rx="30" fill="#9966cc"/>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="white">
    数据治理需要大数据平台+AI分析+数据标准的一体化数据治理解决方案
  </text>
</svg>
