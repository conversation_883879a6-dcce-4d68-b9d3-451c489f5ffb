<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#006699;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0099cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#006699;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="decorGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- 背景渐变 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线元素 -->
  <path d="M 0 200 Q 480 100 960 200 T 1920 200" stroke="url(#decorGradient)" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 0 300 Q 480 200 960 300 T 1920 300" stroke="url(#decorGradient)" stroke-width="2" fill="none" opacity="0.4"/>
  <path d="M 0 880 Q 480 780 960 880 T 1920 880" stroke="url(#decorGradient)" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 0 980 Q 480 880 960 980 T 1920 980" stroke="url(#decorGradient)" stroke-width="2" fill="none" opacity="0.4"/>
  
  <!-- 装饰圆圈 -->
  <circle cx="200" cy="150" r="80" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
  <circle cx="1720" cy="930" r="100" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
  <circle cx="1600" cy="200" r="60" fill="none" stroke="white" stroke-width="1" opacity="0.2"/>
  <circle cx="320" cy="900" r="70" fill="none" stroke="white" stroke-width="1" opacity="0.2"/>
  
  <!-- 主标题区域 -->
  <g transform="translate(960, 300)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="white">
      模块五
    </text>
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="white">
      实战案例分析与总结
    </text>
    <text x="0" y="140" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="white" opacity="0.9">
      Practical Case Analysis & Summary
    </text>
  </g>
  
  <!-- 学习目标 -->
  <g transform="translate(960, 500)">
    <rect x="-400" y="0" width="800" height="300" rx="20" fill="white" fill-opacity="0.15" stroke="white" stroke-width="2"/>
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
      学习目标
    </text>
    
    <g transform="translate(-350, 80)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" fill="white">
        ✓ 分析成功案例经验
      </text>
      <text x="0" y="40" font-family="Microsoft YaHei" font-size="24" fill="white">
        ✓ 总结失败案例教训
      </text>
      <text x="0" y="80" font-family="Microsoft YaHei" font-size="24" fill="white">
        ✓ 综合运用所学知识
      </text>
      <text x="0" y="120" font-family="Microsoft YaHei" font-size="24" fill="white">
        ✓ 制定行动计划
      </text>
      <text x="0" y="160" font-family="Microsoft YaHei" font-size="24" fill="white">
        ✓ 持续改进提升
      </text>
    </g>
  </g>
  
  <!-- 核心内容预览 -->
  <g transform="translate(200, 850)">
    <rect x="0" y="0" width="1520" height="120" rx="20" fill="white" fill-opacity="0.1" stroke="white" stroke-width="1"/>
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
      核心内容
    </text>
    <text x="760" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">
      成功案例分析 • 失败案例反思 • 综合实战演练 • 课程总结提升
    </text>
  </g>
</svg>
