<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="oceanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e6f3ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b3d9ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pieGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pieGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00cc66;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#009944;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pieGradient3" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff9900;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc7700;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pieGradient4" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#cc0066;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#990044;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    蓝海泛舟：智慧派出所市场机遇有多大？
  </text>
  
  <!-- 海浪装饰 -->
  <g transform="translate(100, 50)" opacity="0.3">
    <path d="M0,20 Q20,10 40,20 T80,20" stroke="white" stroke-width="2" fill="none"/>
    <path d="M0,35 Q20,25 40,35 T80,35" stroke="white" stroke-width="2" fill="none"/>
  </g>
  
  <!-- 市场规模计算 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="800" height="300" rx="20" fill="url(#oceanGradient)" stroke="#0066cc" stroke-width="3"/>
    <text x="400" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#0066cc">
      市场规模测算
    </text>
    
    <!-- 计算公式 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="120" height="60" rx="10" fill="#0066cc"/>
      <text x="60" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">4.8万+所</text>
      
      <text x="140" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#333333">×</text>
      
      <rect x="170" y="0" width="80" height="60" rx="10" fill="#00cc66"/>
      <text x="210" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">80%</text>
      
      <text x="270" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#333333">×</text>
      
      <rect x="300" y="0" width="120" height="60" rx="10" fill="#ff9900"/>
      <text x="360" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">X百万/所</text>
      
      <text x="440" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#333333">=</text>
      
      <rect x="470" y="0" width="150" height="60" rx="10" fill="#cc0066"/>
      <text x="545" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">千亿级</text>
      <text x="545" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">市场空间</text>
    </g>
    
    <text x="50" y="180" font-family="Microsoft YaHei" font-size="24" fill="#333333">
      全国派出所总数 × 智能化改造比例 × 平均投资额度 = 巨大市场空间
    </text>
    
    <text x="50" y="220" font-family="Microsoft YaHei" font-size="22" fill="#666666">
      注：具体投资额度因地区、规模、需求而异，从几十万到数千万不等
    </text>
  </g>
  
  <!-- 市场构成饼图 -->
  <g transform="translate(1000, 180)">
    <rect x="0" y="0" width="720" height="300" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="360" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#333333">
      市场构成分析
    </text>
    
    <!-- 饼图 -->
    <g transform="translate(200, 150)">
      <!-- 存量改造升级 (50%) -->
      <path d="M 0,0 L 0,-80 A 80,80 0 0,1 56.57,-56.57 Z" fill="url(#pieGradient1)"/>
      <text x="30" y="-50" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">50%</text>
      
      <!-- 增量新技术 (30%) -->
      <path d="M 0,0 L 56.57,-56.57 A 80,80 0 0,1 56.57,56.57 Z" fill="url(#pieGradient2)"/>
      <text x="60" y="0" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">30%</text>
      
      <!-- 运营服务 (15%) -->
      <path d="M 0,0 L 56.57,56.57 A 80,80 0 0,1 -56.57,56.57 Z" fill="url(#pieGradient3)"/>
      <text x="-10" y="50" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">15%</text>
      
      <!-- GBC联动 (5%) -->
      <path d="M 0,0 L -56.57,56.57 A 80,80 0 0,1 0,-80 Z" fill="url(#pieGradient4)"/>
      <text x="-40" y="-10" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">5%</text>
    </g>
    
    <!-- 图例 -->
    <g transform="translate(350, 100)">
      <rect x="0" y="0" width="20" height="15" fill="url(#pieGradient1)"/>
      <text x="30" y="12" font-family="Microsoft YaHei" font-size="18" fill="#333333">存量改造升级（最大）</text>
      
      <rect x="0" y="30" width="20" height="15" fill="url(#pieGradient2)"/>
      <text x="30" y="42" font-family="Microsoft YaHei" font-size="18" fill="#333333">增量新技术应用</text>
      
      <rect x="0" y="60" width="20" height="15" fill="url(#pieGradient3)"/>
      <text x="30" y="72" font-family="Microsoft YaHei" font-size="18" fill="#333333">持续运营服务</text>
      
      <rect x="0" y="90" width="20" height="15" fill="url(#pieGradient4)"/>
      <text x="30" y="102" font-family="Microsoft YaHei" font-size="18" fill="#333333">GBC联动价值</text>
    </g>
  </g>
  
  <!-- 市场趋势 -->
  <g transform="translate(300, 540)">
    <rect x="0" y="0" width="1320" height="200" rx="20" fill="#fff5f0" stroke="#ff6600" stroke-width="3"/>
    <text x="660" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ff6600">
      市场趋势预判
    </text>
    
    <g transform="translate(100, 80)">
      <!-- 增长趋势 -->
      <rect x="0" y="0" width="500" height="80" rx="15" fill="#00cc66"/>
      <text x="250" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">
        市场将持续增长
      </text>
      <text x="250" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">
        政策驱动 + 需求拉动 + 技术推动
      </text>
      
      <!-- 竞争加剧 -->
      <rect x="620" y="0" width="500" height="80" rx="15" fill="#cc0066"/>
      <text x="870" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">
        竞争将日益激烈
      </text>
      <text x="870" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">
        玩家增多 + 技术同质化 + 价格战
      </text>
    </g>
  </g>
  
  <!-- 机遇总结 -->
  <rect x="400" y="800" width="1120" height="100" rx="50" fill="#0066cc"/>
  <text x="960" y="840" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
    千亿蓝海，时不我待！
  </text>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="white">
    抢占先机，用数据说话，增强学员对市场潜力的感知
  </text>
</svg>
