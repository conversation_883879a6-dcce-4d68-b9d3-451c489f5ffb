<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6600;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc4400;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    SPIN演练指导要点与示范
  </text>
  
  <!-- 场景1示范 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="380" rx="20" fill="#fff5f0" stroke="#ff6600" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ff6600">
      场景1示范：执法记录仪问题深度挖掘
    </text>
    
    <g transform="translate(50, 80)">
      <!-- S问题示范 -->
      <rect x="0" y="0" width="400" height="120" rx="10" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
      <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
        S - 现状问题
      </text>
      <text x="20" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "目前使用什么品牌的执法记录仪？"
      </text>
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "一天大概录制多长时间？"
      </text>
      <text x="20" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "多久需要充电一次？"
      </text>
      <text x="20" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "存储容量是多少？"
      </text>
      
      <!-- P问题示范 -->
      <rect x="420" y="0" width="400" height="120" rx="10" fill="#fff0f0" stroke="#ff6666" stroke-width="2"/>
      <text x="620" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6666">
        P - 问题挖掘
      </text>
      <text x="440" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "电池不耐用具体表现在哪里？"
      </text>
      <text x="440" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "内存满了之后怎么处理？"
      </text>
      <text x="440" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "这种情况多久发生一次？"
      </text>
      <text x="440" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "还有其他什么不便之处？"
      </text>
      
      <!-- I问题示范 -->
      <rect x="840" y="0" width="400" height="120" rx="10" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="1040" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
        I - 影响分析（重点）
      </text>
      <text x="860" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "电池没电会影响执法取证吗？"
      </text>
      <text x="860" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "内存满了会丢失重要证据吗？"
      </text>
      <text x="860" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "这对案件办理有什么影响？"
      </text>
      <text x="860" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "会影响执法规范性吗？"
      </text>
      
      <!-- N问题示范 -->
      <rect x="1260" y="0" width="400" height="120" rx="10" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="1460" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
        N - 需求价值
      </text>
      <text x="1280" y="50" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "如果有续航更久的设备？"
      </text>
      <text x="1280" y="70" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "云端自动备份有什么好处？"
      </text>
      <text x="1280" y="90" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "这样能提升执法效率吗？"
      </text>
      <text x="1280" y="110" font-family="Microsoft YaHei" font-size="16" fill="#333333">
        "对规范执法有什么帮助？"
      </text>
    </g>
    
    <!-- 挖掘路径 -->
    <g transform="translate(50, 220)">
      <rect x="0" y="0" width="1620" height="120" rx="15" fill="#ff6600" opacity="0.1"/>
      <text x="810" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">
        深度挖掘路径示例
      </text>
      
      <g transform="translate(50, 50)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#333333">
          表面问题：
        </text>
        <text x="120" y="0" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          电池不耐用、内存总满
        </text>
        
        <text x="0" y="30" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#333333">
          深层影响：
        </text>
        <text x="120" y="30" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          执法取证不完整 → 案件质量下降 → 执法风险增加
        </text>
        
        <text x="0" y="60" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#333333">
          解决价值：
        </text>
        <text x="120" y="60" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          提升执法规范性 → 保障证据完整性 → 降低执法风险
        </text>
      </g>
    </g>
  </g>
  
  <!-- 关键指导要点 -->
  <g transform="translate(100, 600)">
    <rect x="0" y="0" width="1720" height="300" rx="20" fill="#f0f8ff" stroke="#0066cc" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">
      演练指导关键要点
    </text>
    
    <g transform="translate(50, 80)">
      <!-- 要点1 -->
      <rect x="0" y="0" width="520" height="180" rx="15" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
      <text x="260" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        I（影响）问题是关键
      </text>
      
      <g transform="translate(20, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          重点指导：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 连续追问影响和后果
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 从个人影响到组织影响
        </text>
        <text x="20" y="75" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 从当前影响到长远影响
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 量化影响程度
        </text>
      </g>
      
      <!-- 要点2 -->
      <rect x="540" y="0" width="520" height="180" rx="15" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="800" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        提问技巧要点
      </text>
      
      <g transform="translate(560, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          技巧指导：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 开放式问题为主
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 避免引导性提问
        </text>
        <text x="20" y="75" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 适时总结确认
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 保持好奇心态
        </text>
      </g>
      
      <!-- 要点3 -->
      <rect x="1080" y="0" width="520" height="180" rx="15" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="1340" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
        点评重点
      </text>
      
      <g transform="translate(1100, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          点评要点：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 问题逻辑是否清晰
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 影响挖掘是否深入
        </text>
        <text x="20" y="75" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 价值引导是否到位
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 给出改进建议
        </text>
      </g>
    </g>
  </g>
  
  <!-- 练习成功标准 -->
  <rect x="200" y="940" width="1520" height="120" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
  <text x="960" y="970" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#333333">
    练习成功标准
  </text>
  <text x="960" y="1000" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#0066cc">
    能够从表面问题挖掘到深层影响，引导客户认识解决方案的真正价值
  </text>
  <text x="960" y="1030" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#666666">
    重点：I（影响）问题的深度挖掘能力
  </text>
</svg>
