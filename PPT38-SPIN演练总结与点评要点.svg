<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#006666;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    SPIN演练总结与点评要点
  </text>
  
  <!-- 点评维度 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="120" rx="20" fill="#f0f8f8" stroke="#006666" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#006666">
      点评四大维度
    </text>
    <text x="860" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      问题逻辑 → 挖掘深度 → 价值引导 → 沟通技巧
    </text>
  </g>
  
  <!-- 点评标准 -->
  <g transform="translate(50, 340)">
    <rect x="0" y="0" width="1820" height="400" rx="20" fill="#f5fafa" stroke="#669999" stroke-width="3"/>
    <text x="910" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#006666">
      点评标准与改进建议
    </text>
    
    <g transform="translate(50, 80)">
      <!-- 优秀表现 -->
      <rect x="0" y="0" width="420" height="280" rx="15" fill="#e6f3f3" stroke="#00cc66" stroke-width="2"/>
      <text x="210" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
        优秀表现标准
      </text>
      
      <g transform="translate(20, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
          问题逻辑清晰：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • S-P-I-N顺序合理
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 问题间有逻辑关联
        </text>
        
        <text x="0" y="75" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
          挖掘深度到位：
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 连续追问影响
        </text>
        <text x="20" y="120" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 挖掘根本原因
        </text>
        
        <text x="0" y="150" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
          价值引导有效：
        </text>
        <text x="20" y="175" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 客户认同解决价值
        </text>
        <text x="20" y="195" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 自然引出需求
        </text>
        
        <text x="0" y="225" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
          沟通技巧娴熟：
        </text>
        <text x="20" y="250" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 开放式提问为主
        </text>
      </g>
      
      <!-- 常见问题 -->
      <rect x="440" y="0" width="420" height="280" rx="15" fill="#fff0f0" stroke="#ff6666" stroke-width="2"/>
      <text x="650" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        常见问题识别
      </text>
      
      <g transform="translate(460, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6666">
          逻辑混乱：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 跳跃式提问
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 缺乏递进关系
        </text>
        
        <text x="0" y="75" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6666">
          挖掘不深：
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 停留在表面问题
        </text>
        <text x="20" y="120" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 影响分析不足
        </text>
        
        <text x="0" y="150" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6666">
          引导生硬：
        </text>
        <text x="20" y="175" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 直接推销产品
        </text>
        <text x="20" y="195" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 忽视客户感受
        </text>
        
        <text x="0" y="225" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6666">
          技巧欠缺：
        </text>
        <text x="20" y="250" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 封闭式问题过多
        </text>
      </g>
      
      <!-- 改进建议 -->
      <rect x="880" y="0" width="420" height="280" rx="15" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="1090" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        改进建议方向
      </text>
      
      <g transform="translate(900, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
          强化逻辑训练：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 严格按SPIN顺序
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 每个问题有目的
        </text>
        
        <text x="0" y="75" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
          深化影响挖掘：
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 多问"会导致什么"
        </text>
        <text x="20" y="120" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 量化影响程度
        </text>
        
        <text x="0" y="150" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
          自然价值引导：
        </text>
        <text x="20" y="175" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 让客户自己说出需求
        </text>
        <text x="20" y="195" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 避免直接推销
        </text>
        
        <text x="0" y="225" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
          提升沟通技巧：
        </text>
        <text x="20" y="250" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 多练习开放式提问
        </text>
      </g>
      
      <!-- 实战要点 -->
      <rect x="1320" y="0" width="420" height="280" rx="15" fill="#f0f7ff" stroke="#0066cc" stroke-width="2"/>
      <text x="1530" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        实战应用要点
      </text>
      
      <g transform="translate(1340, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          客户导向：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 真正关心客户问题
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 站在客户角度思考
        </text>
        
        <text x="0" y="75" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          耐心倾听：
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 给客户充分表达时间
        </text>
        <text x="20" y="120" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 捕捉关键信息
        </text>
        
        <text x="0" y="150" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          适时总结：
        </text>
        <text x="20" y="175" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 确认理解正确
        </text>
        <text x="20" y="195" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 强化关键痛点
        </text>
        
        <text x="0" y="225" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          持续练习：
        </text>
        <text x="20" y="250" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 日常工作中应用
        </text>
      </g>
    </g>
  </g>
  
  <!-- 演练成果评估 -->
  <g transform="translate(100, 780)">
    <rect x="0" y="0" width="1720" height="140" rx="20" fill="#fafcff" stroke="#66ccff" stroke-width="3"/>
    <text x="860" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">
      演练成果评估标准
    </text>
    
    <g transform="translate(150, 70)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
        优秀（90分以上）：
      </text>
      <text x="220" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        逻辑清晰，挖掘深入，引导自然，技巧娴熟
      </text>
      
      <text x="0" y="35" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        良好（80-89分）：
      </text>
      <text x="200" y="35" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        基本掌握SPIN逻辑，挖掘有一定深度，需要完善技巧
      </text>
      
      <text x="0" y="70" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        需改进（80分以下）：
      </text>
      <text x="240" y="70" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        逻辑混乱或挖掘不深，需要加强练习
      </text>
    </g>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="960" width="1320" height="100" rx="50" fill="#006666"/>
  <text x="960" y="995" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
    SPIN提问法是顾问式销售的核心技能
  </text>
  <text x="960" y="1030" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
    重点：I（影响）问题的深度挖掘能力
  </text>
</svg>
