<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#666699;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#444466;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    模块二小结 &amp; 思考题
  </text>
  
  <!-- 模块回顾 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="120" rx="20" fill="#f5f5ff" stroke="#666699" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#666699">
      模块二：需求挖掘方法论核心要点
    </text>
    <text x="860" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      从"听需求"到"创需求"的进阶之路
    </text>
  </g>
  
  <!-- 核心收获 -->
  <g transform="translate(50, 340)">
    <rect x="0" y="0" width="1820" height="280" rx="20" fill="#fafaff" stroke="#9999cc" stroke-width="3"/>
    <text x="910" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#666699">
      核心收获总结
    </text>
    
    <g transform="translate(80, 70)">
      <!-- 方法论掌握 -->
      <rect x="0" y="0" width="540" height="180" rx="15" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
      <text x="270" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        方法论掌握
      </text>
      
      <g transform="translate(20, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          三层需求理论：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 听需求：被动响应客户表达
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 挖需求：主动发现潜在痛点
        </text>
        <text x="20" y="65" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 创需求：引导客户认识新价值
        </text>
        
        <text x="0" y="95" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">
          SPIN提问法：
        </text>
        <text x="20" y="120" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 结构化挖掘客户深层需求
        </text>
        <text x="20" y="140" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 重点掌握影响分析技巧
        </text>
      </g>
      
      <!-- 工具应用 -->
      <rect x="560" y="0" width="540" height="180" rx="15" fill="#fff8f0" stroke="#ff9900" stroke-width="2"/>
      <text x="830" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff9900">
        工具应用
      </text>
      
      <g transform="translate(580, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
          调研清单：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 七大维度结构化调研
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 确保痛点挖掘无遗漏
        </text>
        
        <text x="0" y="75" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">
          四维匹配表：
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 需求类型×紧急程度分析
        </text>
        <text x="20" y="120" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 精准匹配解决方案
        </text>
        <text x="20" y="140" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 优化资源配置效率
        </text>
      </g>
      
      <!-- 能力提升 -->
      <rect x="1120" y="0" width="540" height="180" rx="15" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
      <text x="1390" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
        能力提升
      </text>
      
      <g transform="translate(1140, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
          顾问式思维：
        </text>
        <text x="20" y="25" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 从产品推销到顾问咨询
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 真正关心客户业务成功
        </text>
        
        <text x="0" y="75" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#00cc66">
          沟通技巧：
        </text>
        <text x="20" y="100" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 开放式提问技巧
        </text>
        <text x="20" y="120" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 深度倾听和总结确认
        </text>
        <text x="20" y="140" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 价值引导和需求创造
        </text>
      </g>
    </g>
  </g>
  
  <!-- 思考题 -->
  <g transform="translate(100, 660)">
    <rect x="0" y="0" width="1720" height="300" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#666699">
      课后思考题
    </text>
    
    <g transform="translate(50, 80)">
      <!-- 思考题1 -->
      <rect x="0" y="0" width="800" height="180" rx="15" fill="#fff5f5" stroke="#ff6666" stroke-width="2"/>
      <text x="400" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        思考题1：实战应用
      </text>
      
      <g transform="translate(20, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          场景：
        </text>
        <text x="80" y="0" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          客户说"我们的监控设备够用了"
        </text>
        
        <text x="0" y="30" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          任务：
        </text>
        <text x="20" y="55" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 运用SPIN提问法深度挖掘
        </text>
        <text x="20" y="75" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 从"够用"挖掘到"更好用"的需求
        </text>
        <text x="20" y="95" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 设计至少8个递进式问题
        </text>
        <text x="20" y="115" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 重点练习I（影响）问题设计
        </text>
      </g>
      
      <!-- 思考题2 -->
      <rect x="820" y="0" width="800" height="180" rx="15" fill="#f5f8ff" stroke="#0066cc" stroke-width="2"/>
      <text x="1220" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        思考题2：工具应用
      </text>
      
      <g transform="translate(840, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
          任务：
        </text>
        <text x="80" y="0" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          选择一个目标客户进行分析
        </text>
        
        <text x="20" y="30" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 运用《调研清单》收集信息
        </text>
        <text x="20" y="50" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 运用《四维匹配表》分析需求
        </text>
        <text x="20" y="70" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 形成结构化需求分析报告
        </text>
        <text x="20" y="90" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 设计初步解决方案思路
        </text>
        <text x="20" y="110" font-family="Microsoft YaHei" font-size="18" fill="#333333">
          • 准备明天的实战演练
        </text>
      </g>
    </g>
  </g>
  
  <!-- 下一模块预告 -->
  <rect x="200" y="990" width="1520" height="70" rx="35" fill="#666699"/>
  <text x="960" y="1020" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
    下一模块预告
  </text>
  <text x="960" y="1050" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
    模块三：电信"云网边端安"能力深度解析与方案设计
  </text>
</svg>
