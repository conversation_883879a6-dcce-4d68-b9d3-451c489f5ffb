<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="painGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6666;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc3333;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    场景深潜：AI视频监控 - 痛点分析
  </text>
  
  <!-- 现状描述 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="100" rx="20" fill="#f0f7ff" stroke="#0066cc" stroke-width="3"/>
    <text x="860" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">
      AI视频监控现状
    </text>
    <text x="860" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#333333">
      传统视频监控向智能化升级，但面临技术、管理、应用等多重挑战
    </text>
  </g>
  
  <!-- 核心痛点 -->
  <g transform="translate(100, 320)">
    <rect x="0" y="0" width="1720" height="500" rx="20" fill="#fff5f5" stroke="#ff6666" stroke-width="3"/>
    <text x="860" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ff6666">
      核心痛点分析
    </text>
    
    <!-- 痛点1：技术痛点 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6e6" stroke="#ff6666" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        技术痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • AI算法准确率不高
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        误报率高，漏报率高
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 算力资源不足
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        实时分析能力有限
      </text>
    </g>
    
    <!-- 痛点2：管理痛点 -->
    <g transform="translate(480, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6e6" stroke="#ff6666" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        管理痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 设备品牌杂乱
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        统一管理困难
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 运维成本高
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        人力投入大，效率低
      </text>
    </g>
    
    <!-- 痛点3：应用痛点 -->
    <g transform="translate(910, 80)">
      <rect x="0" y="0" width="400" height="180" rx="15" fill="#ffe6e6" stroke="#ff6666" stroke-width="2"/>
      <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        应用痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 数据孤岛严重
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        系统间无法互通
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 智能化程度低
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        仍需大量人工干预
      </text>
    </g>
    
    <!-- 痛点4：成本痛点 -->
    <g transform="translate(1340, 80)">
      <rect x="0" y="0" width="330" height="180" rx="15" fill="#ffe6e6" stroke="#ff6666" stroke-width="2"/>
      <text x="165" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6666">
        成本痛点
      </text>
      
      <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • 建设投入大
      </text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        硬件设备成本高
      </text>
      
      <text x="20" y="125" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
        • ROI不明确
      </text>
      <text x="30" y="150" font-family="Microsoft YaHei" font-size="18" fill="#666666">
        投资回报难量化
      </text>
    </g>
    
    <!-- 具体场景痛点 -->
    <g transform="translate(50, 290)">
      <rect x="0" y="0" width="1620" height="180" rx="15" fill="url(#painGradient)" opacity="0.1"/>
      <text x="810" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff6666">
        具体应用场景痛点
      </text>
      
      <g transform="translate(50, 60)">
        <text x="0" y="0" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          人脸识别场景：
        </text>
        <text x="150" y="0" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          光线影响大、角度要求高、识别速度慢、准确率不稳定
        </text>
        
        <text x="0" y="35" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          行为分析场景：
        </text>
        <text x="150" y="35" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          复杂场景理解能力弱、误报率高、无法处理遮挡情况
        </text>
        
        <text x="0" y="70" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          车辆识别场景：
        </text>
        <text x="150" y="70" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          夜间识别效果差、车牌污损无法识别、车型识别不准确
        </text>
        
        <text x="0" y="105" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#333333">
          异常检测场景：
        </text>
        <text x="150" y="105" font-family="Microsoft YaHei" font-size="20" fill="#333333">
          无法定义复杂异常、环境干扰大、实时性不足
        </text>
      </g>
    </g>
  </g>
  
  <!-- 客户需求总结 -->
  <g transform="translate(200, 860)">
    <rect x="0" y="0" width="1520" height="120" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#333333">
      客户核心需求
    </text>
    
    <g transform="translate(100, 70)">
      <text x="0" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
        技术需求：
      </text>
      <text x="120" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        高精度AI算法、充足算力资源、实时分析能力
      </text>
      
      <text x="700" y="0" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
        管理需求：
      </text>
      <text x="820" y="0" font-family="Microsoft YaHei" font-size="22" fill="#333333">
        统一平台管理、降低运维成本、提升效率
      </text>
    </g>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="1000" width="1320" height="60" rx="30" fill="#ff6666"/>
  <text x="960" y="1040" text-anchor="middle" font-family="Microsoft YaHei" font-size="26" font-weight="bold" fill="white">
    AI视频监控痛点多样化，需要云网融合+AI算力+统一平台的综合解决方案
  </text>
</svg>
