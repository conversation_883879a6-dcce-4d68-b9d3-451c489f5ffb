<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ff9900;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc7700;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    智慧派出所场景框架总览
  </text>
  
  <!-- 中心圆 - 智慧派出所 -->
  <g transform="translate(960, 540)">
    <circle cx="0" cy="0" r="120" fill="url(#centerGradient)"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
      智慧派出所
    </text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">
      八大场景
    </text>
  </g>
  
  <!-- 八大场景圆形布局 -->
  <!-- AI视频监控 -->
  <g transform="translate(960, 240)">
    <circle cx="0" cy="0" r="80" fill="#0066cc"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      AI视频
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      监控
    </text>
    <line x1="0" y1="80" x2="0" y2="180" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 移动警务 -->
  <g transform="translate(1260, 340)">
    <circle cx="0" cy="0" r="80" fill="#00cc66"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      移动
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      警务
    </text>
    <line x1="-57" y1="57" x2="-127" y2="127" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 物联感知 -->
  <g transform="translate(1360, 540)">
    <circle cx="0" cy="0" r="80" fill="#ff6600"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      物联
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      感知
    </text>
    <line x1="-80" y1="0" x2="-280" y2="0" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 数据治理 -->
  <g transform="translate(1260, 740)">
    <circle cx="0" cy="0" r="80" fill="#9966cc"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      数据
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      治理
    </text>
    <line x1="-57" y1="-57" x2="-127" y2="-127" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 指挥调度 -->
  <g transform="translate(960, 840)">
    <circle cx="0" cy="0" r="80" fill="#cc0066"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      指挥
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      调度
    </text>
    <line x1="0" y1="-80" x2="0" y2="-180" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 综合应用 -->
  <g transform="translate(660, 740)">
    <circle cx="0" cy="0" r="80" fill="#ff9900"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      综合
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      应用
    </text>
    <line x1="57" y1="-57" x2="127" y2="-127" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 网络安全 -->
  <g transform="translate(560, 540)">
    <circle cx="0" cy="0" r="80" fill="#ff0000"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      网络
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      安全
    </text>
    <line x1="80" y1="0" x2="280" y2="0" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 运维保障 -->
  <g transform="translate(660, 340)">
    <circle cx="0" cy="0" r="80" fill="#006666"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      运维
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">
      保障
    </text>
    <line x1="57" y1="57" x2="127" y2="127" stroke="#666666" stroke-width="3"/>
  </g>
  
  <!-- 场景说明 -->
  <g transform="translate(200, 180)">
    <rect x="0" y="0" width="400" height="200" rx="15" fill="#f0f7ff" stroke="#0066cc" stroke-width="2"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">
      场景分析维度
    </text>
    
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • 现状痛点
    </text>
    <text x="20" y="100" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • 需求分析
    </text>
    <text x="20" y="130" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • 解决方案
    </text>
    <text x="20" y="160" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • 价值效果
    </text>
  </g>
  
  <!-- 电信优势 -->
  <g transform="translate(1320, 180)">
    <rect x="0" y="0" width="400" height="200" rx="15" fill="#f0fff0" stroke="#00cc66" stroke-width="2"/>
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#00cc66">
      电信核心优势
    </text>
    
    <text x="20" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • 云网融合
    </text>
    <text x="20" y="100" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • 安全可信
    </text>
    <text x="20" y="130" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • 本地服务
    </text>
    <text x="20" y="160" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">
      • DICT能力
    </text>
  </g>
  
  <!-- 总结 -->
  <rect x="300" y="980" width="1320" height="80" rx="40" fill="#ff9900"/>
  <text x="960" y="1030" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">
    八大场景全覆盖，深度理解客户需求，精准匹配电信解决方案
  </text>
</svg>
