<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f7ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e6f3ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    东风已至："科技兴警"战略下的新要求
  </text>
  
  <!-- 风向标图标 -->
  <g transform="translate(100, 50)">
    <circle cx="0" cy="20" r="15" fill="white" opacity="0.8"/>
    <polygon points="0,5 -10,35 10,35" fill="white" opacity="0.8"/>
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" fill="white">东风</text>
  </g>
  
  <!-- 十四五规划 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="800" height="250" rx="20" fill="url(#cardGradient)" stroke="#0066cc" stroke-width="3"/>
    <text x="400" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">
      《"十四五"公安科技创新规划》核心
    </text>
    
    <!-- 关键词标签 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="150" height="40" rx="20" fill="#0066cc"/>
      <text x="75" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">大数据智能化</text>
      
      <rect x="170" y="0" width="120" height="40" rx="20" fill="#0066cc"/>
      <text x="230" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">泛在感知</text>
      
      <rect x="310" y="0" width="120" height="40" rx="20" fill="#0066cc"/>
      <text x="370" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">移动警务</text>
      
      <rect x="450" y="0" width="120" height="40" rx="20" fill="#0066cc"/>
      <text x="510" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">网络安全</text>
    </g>
    
    <text x="50" y="160" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff6600">
      目标：智能化水平全面提升
    </text>
    
    <path d="M50,180 L750,180" stroke="#0066cc" stroke-width="2"/>
    <text x="50" y="210" font-family="Microsoft YaHei" font-size="24" fill="#333333">
      这是公安科技发展的总纲领，为智慧派出所建设指明了方向
    </text>
  </g>
  
  <!-- 大数据指南 -->
  <g transform="translate(1020, 180)">
    <rect x="0" y="0" width="800" height="250" rx="20" fill="url(#cardGradient)" stroke="#0066cc" stroke-width="3"/>
    <text x="400" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">
      《公安大数据智能化建设指南》导向
    </text>
    
    <!-- 关键词标签 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="120" height="40" rx="20" fill="#00cc66"/>
      <text x="60" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">数据驱动</text>
      
      <rect x="140" y="0" width="120" height="40" rx="20" fill="#00cc66"/>
      <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">智能实战</text>
      
      <rect x="280" y="0" width="120" height="40" rx="20" fill="#00cc66"/>
      <text x="340" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">统筹建设</text>
      
      <rect x="420" y="0" width="120" height="40" rx="20" fill="#00cc66"/>
      <text x="480" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">数据共享</text>
      
      <rect x="560" y="0" width="120" height="40" rx="20" fill="#00cc66"/>
      <text x="620" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="white">安全可控</text>
    </g>
    
    <text x="50" y="160" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ff6600">
      强调：数据赋能
    </text>
    
    <path d="M50,180 L750,180" stroke="#00cc66" stroke-width="2"/>
    <text x="50" y="210" font-family="Microsoft YaHei" font-size="24" fill="#333333">
      数据是核心资产，智能化是发展方向
    </text>
  </g>
  
  <!-- 关键信号解读 -->
  <g transform="translate(300, 500)">
    <rect x="0" y="0" width="1320" height="200" rx="20" fill="#fff5f0" stroke="#ff6600" stroke-width="3"/>
    <text x="660" y="40" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ff6600">
      关键信号解读
    </text>
    
    <g transform="translate(100, 80)">
      <!-- 必答题 -->
      <rect x="0" y="0" width="300" height="80" rx="15" fill="#ff6600"/>
      <text x="150" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">必答题</text>
      <text x="150" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">（必须做）</text>
      
      <text x="350" y="40" font-family="Microsoft YaHei" font-size="24" fill="#333333">+</text>
      
      <!-- 高分题 -->
      <rect x="400" y="0" width="300" height="80" rx="15" fill="#0066cc"/>
      <text x="550" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">高分题</text>
      <text x="550" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">（鼓励创新）</text>
      
      <text x="750" y="40" font-family="Microsoft YaHei" font-size="24" fill="#333333">+</text>
      
      <!-- 硬约束 -->
      <rect x="800" y="0" width="300" height="80" rx="15" fill="#cc0066"/>
      <text x="950" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">硬约束</text>
      <text x="950" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="white">（安全合规）</text>
    </g>
  </g>
  
  <!-- 底部总结 -->
  <rect x="200" y="750" width="1520" height="100" rx="50" fill="#0066cc"/>
  <text x="960" y="810" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
    政策解读不是念文件，要提炼核心，解读"潜台词"，明确"为什么要做，要做成什么样"
  </text>
</svg>
