<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="circleGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#0066cc;stop-opacity:0.3" />
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="url(#headerGradient)"/>
  <text x="960" y="75" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="white">
    凝心聚力，破冰启航
  </text>
  
  <!-- 活动规则区域 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="800" height="450" rx="20" fill="#f0f7ff" stroke="#0066cc" stroke-width="3"/>
    <text x="40" y="50" font-family="Microsoft YaHei" font-size="40" font-weight="bold" fill="#0066cc">
      活动规则
    </text>
    
    <!-- 步骤1 -->
    <circle cx="80" cy="120" r="25" fill="#0066cc"/>
    <text x="80" y="130" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">1</text>
    <text x="130" y="130" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      每位学员在便利贴上写下想到"智慧派出所"时
    </text>
    <text x="130" y="165" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      脑海中浮现的3个关键词
    </text>
    
    <!-- 步骤2 -->
    <circle cx="80" cy="220" r="25" fill="#0066cc"/>
    <text x="80" y="230" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">2</text>
    <text x="130" y="230" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      轮流上台，将便利贴贴在白板上
    </text>
    <text x="130" y="265" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      并用一句话解释选择这几个词的原因
    </text>
    
    <!-- 步骤3 -->
    <circle cx="80" cy="320" r="25" fill="#0066cc"/>
    <text x="80" y="330" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="white">3</text>
    <text x="130" y="330" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      快速完成团队组建（按区域或"铁三角"角色）
    </text>
    <text x="130" y="365" font-family="Microsoft YaHei" font-size="28" fill="#333333">
      设计队名、口号
    </text>
    
    <!-- 目的 -->
    <rect x="20" y="390" width="760" height="40" rx="10" fill="#fff5f0"/>
    <text x="40" y="415" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">
      目的：活跃气氛，了解学员对主题的初步认知，快速融入团队
    </text>
  </g>
  
  <!-- 智慧派出所关键词云 -->
  <g transform="translate(1000, 180)">
    <rect x="0" y="0" width="720" height="450" rx="20" fill="#f8f9fa" stroke="#666666" stroke-width="2"/>
    <text x="360" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#333333">
      "智慧派出所"关键词示例
    </text>
    
    <!-- 关键词气泡 -->
    <ellipse cx="200" cy="150" rx="80" ry="35" fill="url(#circleGradient)"/>
    <text x="200" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">AI视频</text>
    
    <ellipse cx="500" cy="130" rx="90" ry="35" fill="url(#circleGradient)"/>
    <text x="500" y="140" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">移动警务</text>
    
    <ellipse cx="150" cy="230" rx="70" ry="35" fill="url(#circleGradient)"/>
    <text x="150" y="240" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">大数据</text>
    
    <ellipse cx="400" cy="220" rx="85" ry="35" fill="url(#circleGradient)"/>
    <text x="400" y="230" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">物联感知</text>
    
    <ellipse cx="600" cy="200" rx="75" ry="35" fill="url(#circleGradient)"/>
    <text x="600" y="210" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">便民服务</text>
    
    <ellipse cx="250" cy="320" rx="80" ry="35" fill="url(#circleGradient)"/>
    <text x="250" y="330" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">智能巡防</text>
    
    <ellipse cx="500" cy="310" rx="90" ry="35" fill="url(#circleGradient)"/>
    <text x="500" y="320" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">数据融合</text>
    
    <text x="360" y="400" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#666666">
      您会想到哪些关键词？
    </text>
  </g>
  
  <!-- 时间提示 -->
  <rect x="760" y="700" width="400" height="80" rx="40" fill="#ff6600"/>
  <text x="960" y="750" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">
    活动时间：10-15分钟
  </text>
</svg>
